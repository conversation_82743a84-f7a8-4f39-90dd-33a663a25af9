{"name": "zod-v3-to-v4", "version": "2.4.0", "description": "Migrate Zod from v3 to v4", "keywords": ["zod", "codemod", "upgrade", "migrate"], "homepage": "https://github.com/nicoespeon/zod-v3-to-v4", "bugs": "https://github.com/nicoespeon/zod-v3-to-v4/issues", "repository": "nicoespeon/zod-v3-to-v4", "license": "MIT", "author": "<PERSON> <<EMAIL>>", "type": "module", "main": "/dist/index.cjs", "bin": {"zod-v3-to-v4": "/dist/index.cjs"}, "files": ["README.md", "/dist/index.cjs"], "scripts": {"prebuild": "<PERSON><PERSON><PERSON> dist", "build": "tsc", "format": "prettier . --write", "format-check": "prettier . --list-different", "prepare": "husky", "prepublishOnly": "typecheck && test", "start": "node --experimental-strip-types --disable-warning=ExperimentalWarning src/index.ts", "test": "vitest run", "test:watch": "vitest watch", "typecheck": "tsc --noEmit"}, "lint-staged": {"*": "prettier --ignore-unknown --write"}, "dependencies": {"@clack/prompts": "1.0.0-alpha.1", "ts-morph": "26.0.0"}, "devDependencies": {"@types/node": "20.9.0", "husky": "9.1.7", "lint-staged": "16.0.0", "prettier": "3.5.3", "prettier-plugin-curly": "0.3.1", "prettier-plugin-packagejson": "2.5.10", "prettier-plugin-sh": "0.15.0", "typescript": "5.8.3", "vitest": "3.1.4", "zod": "3.25.12"}, "packageManager": "pnpm@9.15.4+sha512.b2dc20e2fc72b3e18848459b37359a32064663e5627a51e4c74b2c29dd8e8e0491483c3abb40789cfd578bf362fb6ba8261b05f0387d76792ed6e23ea3b1b6a0"}