{"compilerOptions": {"module": "NodeNext", "target": "ESNext", "moduleResolution": "NodeNext", "lib": ["ESNext", "DOM"], "skipLibCheck": true, "esModuleInterop": true, "resolveJsonModule": true, "rewriteRelativeImportExtensions": true, "allowSyntheticDefaultImports": true, "isolatedModules": true, "jsx": "react-jsx", "useDefineForClassFields": true, "noFallthroughCasesInSwitch": true, "noUnusedLocals": false, "noUnusedParameters": false, "preserveWatchOutput": true, "strict": true, "strictNullChecks": true, "incremental": true, "noUncheckedIndexedAccess": true, "noPropertyAccessFromIndexSignature": false, "allowJs": true}, "include": ["./src/**/*.ts", "./src/**/*.js", "./test/**/*.ts", "./test/**/*.js"], "exclude": ["node_modules", "./dist/**/*"]}